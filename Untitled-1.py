# Gerekli kütüphaneleri içe aktar / Import necessary libraries
import gradio as gr
import cv2
import mediapipe as mp
import numpy as np
from PIL import Image, ImageDraw, ImageFont

# MediaPipe poz tahmin modüllerini başlat / Initialize MediaPipe pose detection modules
mp_pose = mp.solutions.pose
mp_drawing = mp.solutions.drawing_utils
pose = mp_pose.Pose(
    static_image_mode=False,        # Video modu (değil statik görüntü) / Video mode (not static image)
    model_complexity=1,             # Model karmaşıklığı (0=lite, 1=full, 2=heavy) / Model complexity (0=lite, 1=full, 2=heavy)
    enable_segmentation=False,      # Segmentasyon kapalı / Segmentation disabled
    min_detection_confidence=0.5,   # Minimum tespit güveni / Minimum detection confidence
    min_tracking_confidence=0.5     # Minimum takip güveni / Minimum tracking confidence
)

def calculate_angle(a, b, c):
    """
    Üç nokta arasındaki açıyı hesapla / Calculate angle between three points
    a, b, c: [x, y] koordinatları / [x, y] coordinates
    """
    a = np.array(a)  # İlk noktayı numpy array'e çevir / Convert first point to numpy array
    b = np.array(b)  # İkinci noktayı numpy array'e çevir / Convert second point to numpy array
    c = np.array(c)  # Üçüncü noktayı numpy array'e çevir / Convert third point to numpy array
    
    # İki vektör arasındaki açıyı hesapla / Calculate angle between two vectors
    radians = np.arctan2(c[1]-b[1], c[0]-b[0]) - np.arctan2(a[1]-b[1], a[0]-b[0])
    angle = np.abs(radians*180.0/np.pi)  # Radyanları dereceye çevir / Convert radians to degrees
    
    # 180 dereceden büyükse tersini al / If greater than 180 degrees, take the inverse
    if angle > 180.0:
        angle = 360-angle
    
    return angle

def analyze_pose(landmarks):
    """
    Poz analizini yap ve geri bildirim ver / Analyze pose and provide feedback
    landmarks: MediaPipe'dan gelen eklem noktaları / Joint points from MediaPipe
    """
    if not landmarks:
        return "Poz tespit edilemedi / Pose not detected"
    
    feedback = []  # Geri bildirim listesi / Feedback list
    
    try:
        # Sol kol açısını hesapla / Calculate left arm angle
        # Omuz (shoulder), dirsek (elbow), bilek (wrist) noktaları / Shoulder, elbow, wrist points
        left_shoulder = [landmarks[11].x, landmarks[11].y]
        left_elbow = [landmarks[13].x, landmarks[13].y]
        left_wrist = [landmarks[15].x, landmarks[15].y]
        
        left_elbow_angle = calculate_angle(left_shoulder, left_elbow, left_wrist)
        
        # Sağ kol açısını hesapla / Calculate right arm angle
        right_shoulder = [landmarks[12].x, landmarks[12].y]
        right_elbow = [landmarks[14].x, landmarks[14].y]
        right_wrist = [landmarks[16].x, landmarks[16].y]
        
        right_elbow_angle = calculate_angle(right_shoulder, right_elbow, right_wrist)
        
        # Sol diz açısını hesapla / Calculate left knee angle
        left_hip = [landmarks[23].x, landmarks[23].y]
        left_knee = [landmarks[25].x, landmarks[25].y]
        left_ankle = [landmarks[27].x, landmarks[27].y]
        
        left_knee_angle = calculate_angle(left_hip, left_knee, left_ankle)
        
        # Geri bildirim ver / Provide feedback
        feedback.append(f"Sol dirsek açısı / Left elbow angle: {left_elbow_angle:.1f}°")
        feedback.append(f"Sağ dirsek açısı / Right elbow angle: {right_elbow_angle:.1f}°")
        feedback.append(f"Sol diz açısı / Left knee angle: {left_knee_angle:.1f}°")
        
        # Poz değerlendirmesi / Pose evaluation
        if 160 <= left_elbow_angle <= 180:
            feedback.append("✅ Sol kol duruşu iyi / Left arm posture good")
        elif left_elbow_angle < 160:
            feedback.append("⚠️ Sol kolunuzu daha fazla açın / Extend your left arm more")
        
        if 160 <= right_elbow_angle <= 180:
            feedback.append("✅ Sağ kol duruşu iyi / Right arm posture good")
        elif right_elbow_angle < 160:
            feedback.append("⚠️ Sağ kolunuzu daha fazla açın / Extend your right arm more")
            
    except Exception as e:
        feedback.append(f"Analiz hatası / Analysis error: {str(e)}")
    
    return "\n".join(feedback)

def process_video(video_path):
    """
    Yüklenen video dosyasını işle / Process uploaded video file
    video_path: Video dosya yolu / Video file path
    """
    if video_path is None:
        return None
        
    cap = cv2.VideoCapture(video_path)  # Video dosyasını aç / Open video file
    frames = []  # İşlenmiş kareleri sakla / Store processed frames
    
    # Video özelliklerini al / Get video properties
    fps = int(cap.get(cv2.CAP_PROP_FPS))
    width = int(cap.get(cv2.CAP_PROP_FRAME_WIDTH))
    height = int(cap.get(cv2.CAP_PROP_FRAME_HEIGHT))
    
    # Çıktı video yazıcısını hazırla / Prepare output video writer
    fourcc = cv2.VideoWriter_fourcc(*'mp4v')
    output_path = "processed_video.mp4"
    out = cv2.VideoWriter(output_path, fourcc, fps, (width, height))
    
    frame_count = 0  # Kare sayacı / Frame counter
    
    while cap.isOpened():
        ret, frame = cap.read()  # Bir kare oku / Read a frame
        if not ret:
            break
            
        frame_count += 1
        
        # BGR'den RGB'ye dönüştür / Convert BGR to RGB
        rgb_frame = cv2.cvtColor(frame, cv2.COLOR_BGR2RGB)
        
        # Poz tahminini yap / Perform pose estimation
        results = pose.process(rgb_frame)
        
        # RGB'den tekrar BGR'ye çevir çizim için / Convert back to BGR for drawing
        bgr_frame = cv2.cvtColor(rgb_frame, cv2.COLOR_RGB2BGR)
        
        # Sonuçları çiz / Draw results
        if results.pose_landmarks:
            mp_drawing.draw_landmarks(
                bgr_frame, 
                results.pose_landmarks, 
                mp_pose.POSE_CONNECTIONS,
                mp_drawing.DrawingSpec(color=(245,117,66), thickness=2, circle_radius=2),  # Nokta stili / Point style
                mp_drawing.DrawingSpec(color=(245,66,230), thickness=2, circle_radius=2)   # Çizgi stili / Line style
            )
        
        # Kare numarasını ekle / Add frame number
        cv2.putText(bgr_frame, f"Frame: {frame_count}", (10, 30), 
                   cv2.FONT_HERSHEY_SIMPLEX, 1, (0, 255, 0), 2)
        
        out.write(bgr_frame)  # Çıktı videosuna yaz / Write to output video
    
    cap.release()  # Video okuyucuyu kapat / Release video reader
    out.release()  # Video yazıcıyı kapat / Release video writer
    
    return output_path

def process_webcam(frame, confidence_threshold):
    """
    Canlı kamera görüntüsünü işle / Process live webcam frame
    frame: Kamera karesi / Camera frame
    confidence_threshold: Güven eşiği / Confidence threshold
    """
    if frame is None:
        return None, "Kamera verisi yok / No camera data"
    
    # BGR'den RGB'ye dönüştür / Convert BGR to RGB
    rgb_frame = cv2.cvtColor(frame, cv2.COLOR_BGR2RGB)
    
    # Poz tahminini yap / Perform pose estimation
    results = pose.process(rgb_frame)
    
    feedback = ""  # Geri bildirim metni / Feedback text
    
    # Sonuçları çiz ve analiz yap / Draw results and analyze
    if results.pose_landmarks:
        # Güven skoru kontrolü / Confidence score check
        landmarks_above_threshold = [
            lm for lm in results.pose_landmarks.landmark 
            if lm.visibility > confidence_threshold
        ]
        
        # Yeterli nokta varsa çiz / Draw if enough points
        if len(landmarks_above_threshold) > 10:
            mp_drawing.draw_landmarks(
                rgb_frame, 
                results.pose_landmarks, 
                mp_pose.POSE_CONNECTIONS,
                mp_drawing.DrawingSpec(color=(245,117,66), thickness=2, circle_radius=3),
                mp_drawing.DrawingSpec(color=(245,66,230), thickness=2, circle_radius=2)
            )
            
            # Poz analizini yap / Perform pose analysis
            feedback = analyze_pose(results.pose_landmarks.landmark)
        else:
            feedback = f"Yetersiz nokta tespit edildi. Eşiği düşürün. / Insufficient points detected. Lower threshold."
    else:
        feedback = "Poz tespit edilemedi. Daha iyi ışıkta deneyin. / Pose not detected. Try better lighting."
    
    return rgb_frame, feedback

# Gradio arayüzünü oluştur / Create Gradio interface
with gr.Blocks(
    title="Poz Tahmin Uygulaması / Pose Estimation App",
    theme=gr.themes.Soft(),  # Yumuşak tema / Soft theme
    css="footer {visibility: hidden}"  # Footer'ı gizle / Hide footer
) as demo:
    
    # Başlık ve açıklama / Title and description
    gr.Markdown("# 🤸‍♂️ Gelişmiş Poz Tahmin Uygulaması / Advanced Pose Estimation App")
    gr.Markdown("""
    Bu uygulama ile video yükleyebilir veya canlı kameradan poz tahminini gerçek zamanlı izleyebilirsiniz!
    
    With this app, you can upload videos or watch real-time pose estimation from live camera!
    """)
    
    # Video yükleme sekmesi / Video upload tab
    with gr.Tab("📹 Video Yükleme / Video Upload"):
        gr.Markdown("### Video dosyanızı yükleyin ve poz analizini görün / Upload your video file and see pose analysis")
        
        with gr.Row():
            with gr.Column():
                video_input = gr.Video(
                    label="Video Dosyası / Video File",
                    height=400
                )
                video_button = gr.Button(
                    "🔄 Videoyu Analiz Et / Analyze Video", 
                    variant="primary",
                    size="lg"
                )
            
            with gr.Column():
                video_output = gr.Video(
                    label="İşlenmiş Video / Processed Video",
                    height=400
                )
        
        # Video işleme butonu / Video processing button
        video_button.click(
            fn=process_video,
            inputs=video_input,
            outputs=video_output
        )
    
    # Canlı kamera sekmesi / Live camera tab
    with gr.Tab("📷 Canlı Kamera / Live Camera"):
        gr.Markdown("### Canlı kameradan gerçek zamanlı poz tahmin ve analizi / Real-time pose estimation and analysis from live camera")
        
        with gr.Row():
            with gr.Column():
                # Güven eşiği ayarı / Confidence threshold setting
                confidence_slider = gr.Slider(
                    minimum=0.1,
                    maximum=1.0,
                    value=0.5,
                    step=0.1,
                    label="Güven Eşiği / Confidence Threshold",
                    info="Düşük değer = daha fazla nokta, yüksek değer = daha kesin noktalar / Low = more points, high = more precise points"
                )
                
                input_image = gr.Image(
                    sources=["webcam"],  # Sadece webcam kaynağı / Only webcam source
                    streaming=True,      # Canlı akış / Live stream
                    label="Kamera Girişi / Camera Input",
                    height=400
                )
            
            with gr.Column():
                output_image = gr.Image(
                    streaming=True,      # Canlı çıkış / Live output
                    label="Poz Tahmini / Pose Estimation",
                    height=400
                )
                
                feedback_text = gr.Textbox(
                    label="Anlık Poz Analizi / Real-time Pose Analysis",
                    lines=8,
                    max_lines=10,
                    interactive=False
                )
        
        # Canlı işleme arayüzü / Live processing interface
        gr.Interface(
            fn=process_webcam,
            inputs=[input_image, confidence_slider],
            outputs=[output_image, feedback_text],
            live=True,  # Canlı güncelleme / Live update
            allow_flagging="never"  # Bayrak özelliğini kapat / Disable flagging
        )
    
    # Kullanım bilgileri / Usage information
    with gr.Tab("ℹ️ Bilgi / Info"):
        gr.Markdown("""
        ## Nasıl Kullanılır / How to Use
        
        ### Video Yükleme / Video Upload
        1. **Video sekmesini açın / Open video tab**
        2. **Video dosyanızı seçin / Select your video file** (MP4, AVI, MOV desteklenir / supported)
        3. **"Analiz Et" butonuna tıklayın / Click "Analyze" button**
        4. **Sonucu bekleyin / Wait for result**
        
        ### Canlı Kamera / Live Camera
        1. **Kamera sekmesini açın / Open camera tab**
        2. **Kamera izni verin / Grant camera permission**
        3. **Güven eşiğini ayarlayın / Adjust confidence threshold**
        4. **Anlık geri bildirimleri izleyin / Watch real-time feedback**
        
        ## Desteklenen Pozlar / Supported Poses
        - ✅ **Ayakta durma / Standing**
        - ✅ **Kol hareketleri / Arm movements**
        - ✅ **Egzersiz pozları / Exercise poses**
        - ✅ **Yoga pozları / Yoga poses**
        - ✅ **Dans hareketleri / Dance movements**
        
        ## Teknik Detaylar / Technical Details
        - **Model**: MediaPipe Pose
        - **33 eklem noktası / 33 keypoints** tespit eder
        - **Gerçek zamanlı işleme / Real-time processing**
        - **Açı hesaplamaları / Angle calculations**
        - **Otomatik poz değerlendirme / Automatic pose evaluation**
        """)

# Uygulamayı başlat / Launch application
if __name__ == "__main__":
    demo.launch(
        share=True,      # Herkesle paylaş / Share with everyone
        server_name="0.0.0.0",  # Tüm IP'lerden erişim / Access from all IPs
        server_port=7860,       # Port numarası / Port number
        show_api=False,         # API dokümantasyonunu gizle / Hide API documentation
        favicon_path=None       # Varsayılan favicon / Default favicon
    )
